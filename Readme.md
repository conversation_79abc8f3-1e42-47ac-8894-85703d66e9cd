# Employee Rating System

A modern, responsive employee performance evaluation and rating system built with React, TypeScript, and Tailwind CSS. This application provides managers and HR personnel with tools to evaluate employee performance, track team metrics, and generate analytics reports.

## 🚀 Features

- **Dashboard Overview**: Real-time performance metrics and team statistics
- **Employee Management**: Comprehensive team member profiles and information
- **Performance Evaluations**: Structured evaluation forms with multiple criteria
- **Analytics & Reports**: Visual performance analytics and trend analysis
- **Responsive Design**: Mobile-friendly interface built with Tailwind CSS
- **Role-based Access**: Different views for managers and employees

## 📋 Prerequisites

Before running this project, make sure you have the following installed:

- **Node.js**: Version 16.0 or higher
- **npm**: Version 7.0 or higher (comes with Node.js)
- **Git**: For cloning the repository

You can check your current versions by running:
```bash
node --version
npm --version
git --version
```

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd employee_rating
```

### 2. Install Dependencies
```bash
npm install
```

This will install all required dependencies including:
- React 18.3.1
- TypeScript 5.5.3
- Vite 5.4.2
- Tailwind CSS 3.4.1
- Lucide React (for icons)

## 🚀 Development Setup

### Start the Development Server
```bash
npm run dev
```

The application will start and be available at:
- **Local**: http://localhost:5173
- **Network**: Your local IP address will also be displayed

The development server includes:
- Hot Module Replacement (HMR) for instant updates
- TypeScript compilation
- ESLint integration
- Automatic browser refresh

### Access the Application
1. Open your web browser
2. Navigate to `http://localhost:5173`
3. The application will load with a default manager view

## 📁 Project Structure

```
employee_rating/
├── public/                 # Static assets
├── src/
│   ├── components/         # React components
│   │   ├── Analytics.tsx   # Performance analytics dashboard
│   │   ├── Dashboard.tsx   # Main dashboard view
│   │   ├── EvaluationForm.tsx # Employee evaluation form
│   │   ├── Layout.tsx      # Main layout wrapper
│   │   └── TeamManagement.tsx # Team management interface
│   ├── data/
│   │   └── mockData.ts     # Sample data for development
│   ├── types/
│   │   └── index.ts        # TypeScript type definitions
│   ├── utils/              # Utility functions
│   ├── App.tsx             # Main application component
│   ├── main.tsx            # Application entry point
│   ├── index.css           # Global styles and Tailwind imports
│   └── vite-env.d.ts       # Vite environment types
├── package.json            # Project dependencies and scripts
├── vite.config.ts          # Vite configuration
├── tailwind.config.js      # Tailwind CSS configuration
├── tsconfig.json           # TypeScript configuration
└── README.md               # This file
```

## 📜 Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| **Development** | `npm run dev` | Starts the development server with hot reload |
| **Build** | `npm run build` | Creates an optimized production build |
| **Preview** | `npm run preview` | Preview the production build locally |
| **Lint** | `npm run lint` | Run ESLint to check code quality |

### Script Details

- **`npm run dev`**: Starts Vite development server on port 5173
- **`npm run build`**: Compiles TypeScript and creates optimized build in `dist/` folder
- **`npm run preview`**: Serves the production build for testing
- **`npm run lint`**: Runs ESLint with TypeScript support to identify code issues

## 🛠️ Technology Stack

### Frontend Framework
- **React 18.3.1**: Modern React with hooks and concurrent features
- **TypeScript 5.5.3**: Type-safe JavaScript development

### Build Tools
- **Vite 5.4.2**: Fast build tool and development server
- **ESLint**: Code linting and quality assurance

### Styling
- **Tailwind CSS 3.4.1**: Utility-first CSS framework
- **PostCSS**: CSS processing and optimization
- **Autoprefixer**: Automatic vendor prefixing

### Icons & UI
- **Lucide React**: Beautiful, customizable SVG icons

### Development Tools
- **TypeScript ESLint**: TypeScript-specific linting rules
- **React Hooks ESLint**: React hooks linting
- **Vite React Plugin**: React support for Vite

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use
**Problem**: Error message "Port 5173 is already in use"
**Solution**:
```bash
# Kill the process using the port
npx kill-port 5173
# Or use a different port
npm run dev -- --port 3000
```

#### 2. Node Version Issues
**Problem**: "Node version not supported" or similar errors
**Solution**:
- Update Node.js to version 16 or higher
- Use Node Version Manager (nvm) to switch versions:
```bash
nvm install 18
nvm use 18
```

#### 3. Dependency Installation Fails
**Problem**: npm install fails with permission or network errors
**Solution**:
```bash
# Clear npm cache
npm cache clean --force
# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json
# Reinstall dependencies
npm install
```

#### 4. TypeScript Compilation Errors
**Problem**: TypeScript errors during development
**Solution**:
- Check that all dependencies are properly installed
- Restart the TypeScript server in your IDE
- Run type checking manually: `npx tsc --noEmit`

#### 5. Tailwind Styles Not Loading
**Problem**: Tailwind CSS classes not applying
**Solution**:
- Ensure `index.css` imports Tailwind directives
- Check that PostCSS and Tailwind are properly configured
- Restart the development server

#### 6. Hot Reload Not Working
**Problem**: Changes not reflecting in browser
**Solution**:
- Check browser console for errors
- Restart the development server
- Clear browser cache and hard refresh (Ctrl+Shift+R)

### Getting Help

If you encounter issues not covered here:

1. **Check the Console**: Look for error messages in browser developer tools
2. **Verify Dependencies**: Ensure all packages are correctly installed
3. **Check Node Version**: Confirm you're using Node.js 16+
4. **Clear Cache**: Try clearing npm cache and reinstalling dependencies
5. **Restart Services**: Restart your development server and IDE

### Performance Tips

- **Development**: Use `npm run dev` for fastest development experience
- **Production**: Always test with `npm run build` and `npm run preview` before deployment
- **IDE Setup**: Use VS Code with TypeScript and ESLint extensions for best experience

## 🎯 Quick Start Guide

1. **Clone and Install**:
   ```bash
   git clone <repository-url>
   cd employee_rating
   npm install
   ```

2. **Start Development**:
   ```bash
   npm run dev
   ```

3. **Open Browser**: Navigate to `http://localhost:5173`

4. **Start Developing**: The app includes sample data and is ready for development

---

**Happy Coding!** 🎉

For questions or contributions, please refer to the project documentation or contact the development team.